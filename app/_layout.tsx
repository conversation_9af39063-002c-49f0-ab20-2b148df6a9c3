import '@walletconnect/react-native-compat';
import { Poppins_300Light } from '@expo-google-fonts/poppins/300Light';
import { Poppins_400Regular } from '@expo-google-fonts/poppins/400Regular';
import { Poppins_500Medium } from '@expo-google-fonts/poppins/500Medium';
import { Poppins_600SemiBold } from '@expo-google-fonts/poppins/600SemiBold';
import { Poppins_700Bold } from '@expo-google-fonts/poppins/700Bold';
import { useFonts } from '@expo-google-fonts/poppins/useFonts';
import { AppKit, createAppKit, defaultWagmiConfig } from '@reown/appkit-wagmi-react-native';
import { sepolia } from '@wagmi/core/chains';
import { router, Stack, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus, Keyboard, PanResponder, StyleSheet, View } from 'react-native';
import { install } from 'react-native-quick-crypto';
import ToastManager from 'toastify-react-native';
import { WagmiProvider } from 'wagmi';
import { useGetPin } from '@/hooks/useGetPin';
import { useTheme } from '@/hooks/useThemeColor';
import Providers from '@/utils/providers';

import 'react-native-reanimated';
import { getApp } from '@react-native-firebase/app';
import { onMessage } from '@react-native-firebase/messaging';
import { useCommonStore } from '@/store/common';
import { env } from '@/utils/env';
import { toastConfig } from '@/utils/toast';

install();

// 1. Get projectId
const projectId = env.WALLET_CONNECT_PROJECT_ID;

// 2. Create config
const metadata = {
  name: 'AppKit RN',
  description: 'AppKit RN Example',
  url: 'https://reown.com/appkit',
  icons: ['https://avatars.githubusercontent.com/u/179229932'],
  redirect: {
    native: 'YOUR_APP_SCHEME://',
    universal: 'YOUR_APP_UNIVERSAL_LINK.com',
  },
};

const chains = [sepolia] as const;

const wagmiConfig = defaultWagmiConfig({
  chains,
  projectId,
  metadata,
});

// 3. Create modal
createAppKit({
  projectId,
  metadata,
  wagmiConfig,
  defaultChain: sepolia,
  // enableAnalytics: true,
  // connectorImages: {
  //   coinbaseWallet:
  //     'https://play-lh.googleusercontent.com/wrgUujbq5kbn4Wd4tzyhQnxOXkjiGqq39N4zBvCHmxpIiKcZw_Pb065KTWWlnoejsg',
  //   appKitAuth: 'https://avatars.githubusercontent.com/u/179229932',
  // },
});

// 5 minutes in milliseconds
const INACTIVITY_TIMEOUT = 5 * 60 * 1000;
// Check every 30 seconds
const CHECK_INTERVAL = 30 * 1000;

function LayoutWrapper() {
  const { styles } = useStyles();

  const isFirstTime = useCommonStore.use.isFirstTime();
  const [appState, setAppState] = useState<AppStateStatus>();
  const lastActiveTime = useRef(Date.now());
  const timeoutRef = useRef<number | undefined>(undefined);

  const navigation = useNavigation();

  const { data: hashingPassCode, isPending: isFetchingPin } = useGetPin();

  const resetInactivityTimer = useCallback(() => {
    Keyboard.dismiss();
    lastActiveTime.current = Date.now();
  }, []);

  const checkInactivity = useCallback(() => {
    const timeSinceLastActivity = Date.now() - lastActiveTime.current;
    if (!hashingPassCode) return;
    if (timeSinceLastActivity >= INACTIVITY_TIMEOUT) {
      router.navigate('/auth');
    }
  }, [hashingPassCode]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('state', () => {
      resetInactivityTimer();
    });

    return unsubscribe;
  }, [navigation, resetInactivityTimer]);

  useEffect(() => {
    if (isFetchingPin || isFirstTime) return;

    if (!appState && !!hashingPassCode) {
      router.navigate('/auth');
    }

    setAppState(AppState.currentState);

    const appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      setAppState(nextAppState);
      if (nextAppState === 'active') {
        resetInactivityTimer();
      }
    });

    if (!hashingPassCode) return;

    timeoutRef.current = setInterval(checkInactivity, CHECK_INTERVAL);

    return () => {
      appStateSubscription.remove();
      if (timeoutRef.current) {
        clearInterval(timeoutRef.current);
      }
    };
  }, [appState, hashingPassCode, isFetchingPin, isFirstTime, checkInactivity, resetInactivityTimer]);

  useEffect(() => {
    const firebaseApp = getApp();

    const unsubscribe = onMessage(firebaseApp.messaging(), async (remoteMessage) => {
      console.log({
        remoteMessage,
      });
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        resetInactivityTimer();
        return false;
      },
      onMoveShouldSetPanResponderCapture: () => {
        resetInactivityTimer();
        return false;
      },
    })
  ).current;

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <Stack
        screenOptions={{
          headerStyle: styles.headerStyle,
          contentStyle: styles.container,
          headerTitleAlign: 'center',
          headerTintColor: '#fff',
          headerBackButtonDisplayMode: 'minimal',
          headerBackButtonMenuEnabled: false,
          headerBackTitle: 'Back',
        }}
        initialRouteName='(app)'
      >
        <Stack.Screen name='(app)' options={{ headerShown: false }} />

        <Stack.Screen name='(new-wallet)' options={{ headerShown: false }} />

        <Stack.Screen name='(recovery-wallet)' options={{ headerShown: false }} />

        <Stack.Screen name='onboarding' options={{ headerShown: false }} />

        <Stack.Screen name='auth' options={{ headerShown: false, gestureEnabled: false }} />

        <Stack.Screen name='+not-found' />
      </Stack>

      <AppKit />

      <StatusBar style='light' />

      <ToastManager config={toastConfig} useModal={false} />
    </View>
  );
}

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    // Poppins_100Thin,
    // Poppins_100Thin_Italic,
    // Poppins_200ExtraLight,
    // Poppins_200ExtraLight_Italic,
    Poppins_300Light,
    // Poppins_300Light_Italic,
    Poppins_400Regular,
    // Poppins_400Regular_Italic,
    Poppins_500Medium,
    // Poppins_500Medium_Italic,
    Poppins_600SemiBold,
    // Poppins_600SemiBold_Italic,
    Poppins_700Bold,
    // Poppins_700Bold_Italic,
    // Poppins_800ExtraBold,
    // Poppins_800ExtraBold_Italic,
    // Poppins_900Black,
    // Poppins_900Black_Italic
  });

  if (!fontsLoaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <WagmiProvider config={wagmiConfig}>
      <Providers>
        <LayoutWrapper />
      </Providers>
    </WagmiProvider>
  );
}

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    headerStyle: {
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
