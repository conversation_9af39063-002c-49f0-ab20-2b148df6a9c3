import { Poppins_300Light } from '@expo-google-fonts/poppins/300Light';
import { Poppins_400Regular } from '@expo-google-fonts/poppins/400Regular';
import { Poppins_500Medium } from '@expo-google-fonts/poppins/500Medium';
import { Poppins_600SemiBold } from '@expo-google-fonts/poppins/600SemiBold';
import { Poppins_700Bold } from '@expo-google-fonts/poppins/700Bold';
import { useFonts } from '@expo-google-fonts/poppins/useFonts';
import { router, Stack, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus, Keyboard, PanResponder, StyleSheet, View } from 'react-native';
import { install } from 'react-native-quick-crypto';
import ToastManager from 'toastify-react-native';
import { useGetPin } from '@/hooks/useGetPin';
import { useTheme } from '@/hooks/useThemeColor';
import Providers from '@/utils/providers';

import 'react-native-reanimated';
import { toastConfig } from '@/utils/toast';

install();

// 5 minutes in milliseconds
const INACTIVITY_TIMEOUT = 5 * 60 * 1000;
// Check every 30 seconds
const CHECK_INTERVAL = 30 * 1000;

function LayoutWrapper() {
  const { styles } = useStyles();
  const [appState, setAppState] = useState<AppStateStatus>();
  const lastActiveTime = useRef(Date.now());
  const timeoutRef = useRef<number | undefined>(undefined);

  const navigation = useNavigation();

  const { data: hashingPassCode, isPending: isFetchingPin } = useGetPin();

  const resetInactivityTimer = useCallback(() => {
    Keyboard.dismiss();
    lastActiveTime.current = Date.now();
  }, []);

  const checkInactivity = useCallback(() => {
    const timeSinceLastActivity = Date.now() - lastActiveTime.current;
    if (!hashingPassCode) return;
    if (timeSinceLastActivity >= INACTIVITY_TIMEOUT) {
      if (!__DEV__) router.navigate('/auth');
    }
  }, [hashingPassCode]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('state', () => {
      resetInactivityTimer();
    });

    return unsubscribe;
  }, [navigation, resetInactivityTimer]);

  useEffect(() => {
    if (isFetchingPin) return;

    if (!appState && !!hashingPassCode) {
      if (!__DEV__) router.navigate('/auth');
    }

    setAppState(AppState.currentState);

    const appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      setAppState(nextAppState);
      if (nextAppState === 'active') {
        resetInactivityTimer();
      }
    });

    if (!hashingPassCode) return;

    timeoutRef.current = setInterval(checkInactivity, CHECK_INTERVAL);

    return () => {
      appStateSubscription.remove();
      if (timeoutRef.current) {
        clearInterval(timeoutRef.current);
      }
    };
  }, [appState, hashingPassCode, isFetchingPin, checkInactivity, resetInactivityTimer]);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        resetInactivityTimer();
        return false;
      },
      onMoveShouldSetPanResponderCapture: () => {
        resetInactivityTimer();
        return false;
      },
    })
  ).current;

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <Stack
        screenOptions={{
          headerStyle: styles.headerStyle,
          contentStyle: styles.container,
          headerTitleAlign: 'center',
          headerTintColor: '#fff',
          headerBackButtonDisplayMode: 'minimal',
          headerBackButtonMenuEnabled: false,
          headerBackTitle: 'Back',
        }}
        initialRouteName='(app)'
      >
        <Stack.Screen name='(app)' options={{ headerShown: false }} />

        <Stack.Screen name='(new-wallet)' options={{ headerShown: false }} />

        <Stack.Screen name='(recovery-wallet)' options={{ headerShown: false }} />

        <Stack.Screen name='onboarding' options={{ headerShown: false }} />

        <Stack.Screen name='auth' options={{ headerShown: false, gestureEnabled: false }} />

        <Stack.Screen name='+not-found' />
      </Stack>

      <StatusBar style='light' />

      <ToastManager config={toastConfig} useModal={false} />
    </View>
  );
}

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    // Poppins_100Thin,
    // Poppins_100Thin_Italic,
    // Poppins_200ExtraLight,
    // Poppins_200ExtraLight_Italic,
    Poppins_300Light,
    // Poppins_300Light_Italic,
    Poppins_400Regular,
    // Poppins_400Regular_Italic,
    Poppins_500Medium,
    // Poppins_500Medium_Italic,
    Poppins_600SemiBold,
    // Poppins_600SemiBold_Italic,
    Poppins_700Bold,
    // Poppins_700Bold_Italic,
    // Poppins_800ExtraBold,
    // Poppins_800ExtraBold_Italic,
    // Poppins_900Black,
    // Poppins_900Black_Italic
  });

  if (!fontsLoaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <Providers>
      <LayoutWrapper />
    </Providers>
  );
}

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    headerStyle: {
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
