import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { sponsorRequest } from '@/apis/sponsor';
import { useSafeContext } from '@/context/safeContext';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';
import { apiKit, getAuthorization } from '@/utils/web3';

export const useExecuteSafeTx = () => {
  const { safeWallet, safeInstance } = useSafeContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ safeTxHash }: { safeTxHash: string }) => {
      if (!safeWallet) throw new Error('No safe wallet found');
      if (!safeInstance) throw new Error('No safe instance found');

      const walletClient = await safeInstance.getSafeProvider().getExternalSigner();
      if (!walletClient) throw new Error('No wallet client found');

      const safeMultiSigTransaction = await apiKit.getTransaction(safeTxHash);
      const safeTransaction = await safeInstance.toSafeTransactionType(safeMultiSigTransaction);
      const encodedTx = (await safeInstance.getEncodedTransaction(safeTransaction)) as Hex;

      const serializedAuth = await getAuthorization(walletClient, env.GEOSAFE_CONTRACT_ADDRESS);
      const hash = await sponsorRequest({ txData: encodedTx, to: safeWallet, serializedAuth });
      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });

      return receipt;
    },
    onSuccess() {
      queryClient.invalidateQueries({
        queryKey: queryKeys.safeTxsPending,
      });
    },
  });
};
