import { TokenInfoResponse } from '@safe-global/api-kit';
import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { isAddress } from 'viem';
import { queryKeys } from '@/utils/queryKeys';
import { apiKit } from '@/utils/web3';

export const useGetTokenInfo = (
  { tokenAddress }: { tokenAddress: string },
  queryOptions?: UseQueryOptions<TokenInfoResponse, Error, TokenInfoResponse>
) => {
  return useQuery({
    queryKey: queryKeys.tokenInfo.concat([tokenAddress!]),
    queryFn: async () => {
      const tokenInfo = await apiKit.getToken(tokenAddress);
      return tokenInfo;
    },
    enabled: !!tokenAddress && isAddress(tokenAddress),
    ...queryOptions,
  });
};
