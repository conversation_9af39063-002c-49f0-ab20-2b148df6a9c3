import SafeApiKit from '@safe-global/api-kit';
import { useMutation } from '@tanstack/react-query';
import { encodeFunctionData, erc20Abi, parseUnits, zeroAddress } from 'viem';
import { privateKeyToAddress } from 'viem/accounts';
import { useSafeContext } from '@/context/safeContext';
import { env } from '@/utils/env';
import { CURRENT_CHAIN } from '@/utils/web3';

type Payload = {
  recipientAddress: string;
  tokenAddress: string;
  amount: string;
  decimal: number;
};

export const useCreateTxTransferToken = () => {
  const { safeInstance, safeWallet, signer } = useSafeContext();

  return useMutation({
    mutationFn: async ({ recipientAddress, tokenAddress, amount, decimal }: Payload) => {
      if (!safeWallet) throw new Error('No safe wallet found');
      if (!safeInstance) throw new Error('No safe instance found');
      if (!signer) throw new Error('No signer found');
      const signerAddress = privateKeyToAddress(`0x${signer.privateKey}`);

      let transaction;

      if (tokenAddress === zeroAddress) {
        transaction = {
          to: recipientAddress,
          data: '0x',
          value: parseUnits(amount, decimal).toString(),
        } as const;
      } else {
        const transactionData = encodeFunctionData({
          abi: erc20Abi,
          functionName: 'transfer',
          args: [recipientAddress, parseUnits(amount, decimal)],
        });

        transaction = {
          to: tokenAddress,
          data: transactionData,
          value: '0',
        } as const;
      }

      let safeTransaction = await safeInstance.createTransaction({
        transactions: [transaction],
      });

      const apiKit = new SafeApiKit({
        chainId: BigInt(CURRENT_CHAIN.id),
        apiKey: env.SAFE_API_KEY,
      });

      // Deterministic hash based on transaction parameters
      const safeTxHash = await safeInstance.getTransactionHash(safeTransaction);

      // Sign transaction to verify that the transaction is coming from owner 1
      const senderSignature = await safeInstance.signHash(safeTxHash);

      await apiKit.proposeTransaction({
        safeAddress: safeWallet,
        safeTransactionData: safeTransaction.data,
        safeTxHash,
        senderAddress: signerAddress,
        senderSignature: senderSignature.data,
      });
    },
  });
};
