import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query';
import { useSafeContext } from '@/context/safeContext';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';

type ERC20 = {
  name: string;
  decimals: number;
  symbol: string;
  logoUri: string;
};

type TokenResult = {
  tokenAddress?: string;
  balance: string;
  token?: ERC20;
};

type GetBalancesResponse = {
  count: number;
  next: string;
  previous: string;
  results: TokenResult;
};

export const useGetSafeBalances = (
  { limit = 10 },
  queryOptions?: UseInfiniteQueryOptions<GetBalancesResponse, Error, InfiniteData<GetBalancesResponse>, any, number>
) => {
  const { safeWallet } = useSafeContext();

  return useInfiniteQuery({
    queryKey: queryKeys.safeWalletInfo.concat([safeWallet!]),
    queryFn: async (pageParam) => {
      if (!safeWallet) throw new Error('No safe wallet found');

      const searchQuery = new URLSearchParams({
        limit: limit.toString(),
        offset: (pageParam.pageParam * limit).toString(),
        trusted: 'false',
        exclude_spam: 'false',
      });

      const url = `https://api.safe.global/tx-service/sep/api/v2/safes/${safeWallet}/balances/?` + searchQuery;

      const data: GetBalancesResponse = await fetch(url, {
        method: 'GET',
        headers: {
          'content-type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${env.SAFE_API_KEY}`,
        },
      }).then((res) => res.json());

      return data;
    },
    getNextPageParam: (lastPage, allPages = [], lastPageParam) => {
      return lastPage?.next ? Number(lastPageParam + 1) : undefined;
    },
    initialPageParam: 0,
    enabled: !!safeWallet,
    ...queryOptions,
  });
};
