{"name": "geo-safe", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "format": "biome format .", "format:fix": "biome format .  --write", "postinstall": "husky", "prepare": "husky", "build-apk": "cd android && ./gradlew app:assembleRelease && cd ..", "prebuild": "expo prebuild", "type-check": "tsc --noEmit"}, "dependencies": {"@babel/plugin-transform-class-static-block": "^7.27.1", "@d11/react-native-fast-image": "^8.11.0", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.8", "@hookform/resolvers": "^5.2.1", "@mantine/hooks": "^8.2.2", "@react-native-firebase/app": "^23.0.1", "@react-native-firebase/messaging": "^23.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@safe-global/api-kit": "^4.0.0", "@safe-global/protocol-kit": "^6.1.0", "@safe-global/types-kit": "^3.0.0", "@scure/bip32": "^1.7.0", "@scure/bip39": "^1.6.0", "@tanstack/react-query": "^5.83.0", "auto-zustand-selectors-hook": "^3.0.1", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "expo": "~53.0.17", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linking": "~7.1.7", "expo-local-authentication": "~16.0.5", "expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "h3-react-native": "github:lephuochoai/h3-react-native", "js-big-decimal": "^2.2.0", "ngeohash": "^0.6.3", "react": "19.0.0", "react-dom": "19.0.0", "react-fancy-qrcode": "^1.0.4", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "~2.24.0", "react-native-jazzicon": "^0.1.2", "react-native-keyboard-controller": "^1.17.5", "react-native-mmkv": "^3.3.0", "react-native-quick-crypto": "^0.7.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "toastify-react-native": "^7.2.3", "viem": "^2.33.0", "zod": "^4.0.17", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@biomejs/biome": "^2.1.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/ngeohash": "^0.6.8", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "husky": "^9.1.7", "typescript": "~5.8.3"}, "private": true, "packageManager": "pnpm@9.14.2+sha512.6e2baf77d06b9362294152c851c4f278ede37ab1eba3a55fda317a4a17b209f4dbb973fb250a77abc463a341fcb1f17f17cfa24091c4eb319cda0d9b84278387"}