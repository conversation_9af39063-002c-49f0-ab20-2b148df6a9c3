import { Pressable, StyleSheet } from 'react-native';
import { Toast } from 'toastify-react-native';
import { ToastShowParams } from 'toastify-react-native/utils/interfaces';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';

export const toastConfig = {
  success: (props: ToastShowParams) => (
    <Pressable style={[styles.successContainer, { bottom: 80, position: 'absolute' }]} onPress={props.onPress}>
      <Show when={!!props.text1}>
        <ThemedText style={styles.successText} type='smallMedium'>
          {props.text1}
        </ThemedText>
      </Show>

      <ThemedText style={styles.successText} type='smallLight'>
        {props.text2}
      </ThemedText>
    </Pressable>
  ),
  error: (props: ToastShowParams) => (
    <Pressable style={[styles.errorContainer, { bottom: 80, position: 'absolute' }]} onPress={props.onPress}>
      <Show when={!!props.text1}>
        <ThemedText style={styles.errorText} type='smallMedium'>
          {props.text1}
        </ThemedText>
      </Show>

      <ThemedText style={styles.errorText} type='smallLight'>
        {props.text2}
      </ThemedText>
    </Pressable>
  ),
};

const styles = StyleSheet.create({
  successContainer: {
    backgroundColor: '#0C4539',
    paddingVertical: 9,
    paddingHorizontal: 16,
    borderRadius: 999,
    marginHorizontal: 16,
  },
  successText: {
    textAlign: 'center',
    color: Colors.dark.primary,
  },
  errorContainer: {
    backgroundColor: '#c80a0aff',
    paddingVertical: 9,
    paddingHorizontal: 16,
    borderRadius: 999,
    marginHorizontal: 16,
  },
  errorText: {
    textAlign: 'center',
    color: '#fafafaff',
  },
});

export const toastSuccess = (description: string, title?: string, options?: ToastShowParams) => {
  Toast.show({
    ...options,
    type: 'success',
    text1: title,
    text2: description,
    position: 'bottom',
    visibilityTime: 4000,
    autoHide: true,
    useModal: false,
    onPress: () => Toast.hide(),
  });
};

export const toastError = (error: any, options?: ToastShowParams) => {
  console.error(error);
  const errorMessage =
    typeof error === 'string' ? error : (error?.message ?? (typeof error === 'string' ? error : 'An error occurred'));

  Toast.show({
    type: 'error',
    text1: '',
    text2: errorMessage,
    position: 'bottom',
    visibilityTime: 4000,
    autoHide: true,
    useModal: false,
    onPress: () => Toast.hide(),
    ...options,
  });
};
