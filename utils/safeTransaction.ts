import { DecodedParameters, SafeMultisigTransactionResponse } from '@safe-global/types-kit';

export const classifyByMethod = (tx: SafeMultisigTransactionResponse) => {
  if (tx.dataDecoded && tx.dataDecoded.method) {
    const method = tx.dataDecoded.method;

    switch (method) {
      case 'transfer':
      case 'transferFrom':
        return 'TOKEN_TRANSFER';

      case 'approve':
        return 'TOKEN_APPROVAL';

      case 'addOwnerWithThreshold':
      case 'removeOwner':
      case 'changeThreshold':
        return 'SAFE_CONFIG_CHANGE';

      case 'multiSend':
        return 'BATCH_TRANSACTION';

      default:
        return `CONTRACT_METHOD_${method.toUpperCase()}`;
    }
  }

  if ((tx.data === '0x' || !tx.data) && tx.value === '0' && (!tx.dataDecoded || !tx.dataDecoded.method)) {
    return 'REJECTION';
  }

  return 'NO_METHOD_DECODED';
};

export const classifyByTypeAndValue = (tx: SafeMultisigTransactionResponse) => {
  if (Number(tx.value) > 0) {
    return 'ETH_TRANSFER';
  }

  if (tx.to && tx.data && tx.data !== '0x') {
    return 'CONTRACT_INTERACTION';
  }

  if (!tx.to || tx.to === '******************************************') {
    return 'CONTRACT_CREATION';
  }

  return 'UNKNOWN';
};

export const classifyByOperation = (tx: SafeMultisigTransactionResponse) => {
  switch (tx.operation) {
    case 0:
      return 'CALL';
    case 1:
      return 'DELEGATE_CALL';
    case 2:
      return 'CREATE';
    default:
      return 'UNKNOWN';
  }
};

export const determineCategory = (valueType: string, method: string) => {
  if (valueType === 'ETH_TRANSFER') return 'ETH_TRANSFER';
  if (method === 'TOKEN_TRANSFER') return 'TOKEN_TRANSFER';
  if (method === 'TOKEN_APPROVAL') return 'TOKEN_APPROVAL';
  if (method === 'SAFE_CONFIG_CHANGE') return 'SAFE_MANAGEMENT';
  if (method === 'BATCH_TRANSACTION') return 'BATCH_OPERATION';
  if (valueType === 'CONTRACT_INTERACTION') return 'DAPP_INTERACTION';
  if (valueType === 'CONTRACT_CREATION') return 'CONTRACT_DEPLOYMENT';
  if (method === 'REJECTION') return 'REJECTION';

  return 'OTHER';
};

export const getTokenInfoFromTransaction = (tx: SafeMultisigTransactionResponse) => {
  if (tx.value && tx.value !== '0') {
    return {
      tokenType: 'ETH',
      amount: tx.value, // Wei format
      tokenAddress: null,
      tokenInfo: {
        symbol: 'ETH',
        decimals: 18,
        name: 'Ethereum',
      },
    };
  }

  if (tx.dataDecoded && tx.dataDecoded.method) {
    const method = tx.dataDecoded.method;
    const parameters = tx.dataDecoded.parameters;

    switch (method) {
      case 'transfer':
        return {
          tokenType: 'ERC20',
          tokenAddress: tx.to,
          recipient: parameters.find((p) => p.name === 'to')?.value,
          amount: parameters.find((p) => p.name === 'value')?.value,
        };

      case 'transferFrom':
        return {
          tokenType: 'ERC20',
          tokenAddress: tx.to,
          from: parameters.find((p) => p.name === 'from')?.value,
          recipient: parameters.find((p) => p.name === 'to')?.value,
          amount: parameters.find((p) => p.name === 'value')?.value,
        };

      case 'multiSend':
        return parseMultiSendTransactions(parameters);
    }
  }

  return null;
};

const parseMultiSendTransactions = (parameters: DecodedParameters[]) => {
  const transactionsParam = parameters.find((p) => p.name === 'transactions');

  if (transactionsParam && transactionsParam.valueDecoded) {
    return transactionsParam.valueDecoded.map((decoded) => ({
      tokenType: 'MULTI_SEND',
      operation: decoded.operation,
      to: decoded.to,
      value: decoded.value,
      data: decoded.data,
      dataDecoded: decoded.dataDecoded,
    }));
  }

  return null;
};
