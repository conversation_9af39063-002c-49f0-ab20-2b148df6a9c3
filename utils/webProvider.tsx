import '@walletconnect/react-native-compat';

import { createAppKit, defaultWagmiConfig } from '@reown/appkit-wagmi-react-native';
import { sepolia } from '@wagmi/core/chains';
import { WagmiProvider } from 'wagmi';
import { env } from './env';



export const WagmiProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  return <WagmiProvider config={wagmiConfig}>{children}</WagmiProvider>;
};
