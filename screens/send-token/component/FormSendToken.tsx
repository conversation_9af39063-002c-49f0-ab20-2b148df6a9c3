import * as Clipboard from 'expo-clipboard';
import { Controller, useFormContext } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';
import { useTheme } from '@/hooks/useThemeColor';
import { TransferFormData } from '../transferTokenSchema';
import { ChooseAsset } from './ChooseAsset';

type Props = {};

export const FormSendToken = (props: Props) => {
  const { styles, colors } = useStyles();

  const { data: safeTokens } = useGetSafeBalances({ limit: 10 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results) || [];

  const methods = useFormContext<TransferFormData>();
  const {
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = methods;

  async function handlePaste() {
    const content = await Clipboard.getStringAsync();
    setValue('recipientAddress', content, { shouldValidate: true });
  }

  return (
    <View style={styles.container}>
      <View>
        <ThemedText type='defaultSemiBold'>New transaction</ThemedText>

        <Spacer height={8} />

        <ThemedText type='default'>Send tokens</ThemedText>
      </View>

      <View style={styles.divider} />

      <Controller
        control={control}
        name='recipientAddress'
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            placeholder='Enter recipient address'
            placeholderTextColor='#666'
            value={value}
            label='Recipient address'
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.recipientAddress?.message}
            editable={!isSubmitting}
            onRightIconPress={handlePaste}
            rightIcon={<Icons.Paste size={24} color={colors.white65} />}
          />
        )}
      />

      <ChooseAsset tokens={tokens} />

      <Controller
        control={control}
        name='amount'
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            placeholder='Enter amount'
            placeholderTextColor='#666'
            value={value}
            label='Amount'
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.amount?.message}
            editable={!isSubmitting}
          />
        )}
      />
    </View>
  );
};

const useStyles = () => {
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
      gap: 16,
    },
    divider: {
      borderBottomWidth: 1,
      borderBottomColor: white65,
    },
  });

  return { styles, colors: { white65 } };
};
