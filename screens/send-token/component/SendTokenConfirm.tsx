import { useFormContext } from 'react-hook-form';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { CustomImage } from '@/components/ui/Image';
import { TokenResult } from '@/hooks/useGetSafeBalances';
import { useTheme } from '@/hooks/useThemeColor';
import { prettyNumber } from '@/utils/common';
import { TransferFormData } from '../transferTokenSchema';

type Props = {
  currentToken?: TokenResult;
};

export const SendTokenConfirm = ({ currentToken }: Props) => {
  const { styles } = useStyles();

  const methods = useFormContext<TransferFormData>();
  const { watch } = methods;
  const [recipientAddress, amount] = watch(['recipientAddress', 'amount']);

  return (
    <ScrollView bounces={false} showsVerticalScrollIndicator={false} contentContainerStyle={{ gap: 16, padding: 10 }}>
      <View>
        <ThemedText type='defaultSemiBold'>Confirm transaction</ThemedText>

        <Spacer height={8} />

        <ThemedText type='default'>Send tokens</ThemedText>
      </View>

      <View style={styles.divider} />

      <View style={styles.center}>
        <CustomImage source={{ uri: currentToken?.token?.logoUri }} style={styles.tokenLogo} />

        <Spacer height={8} />

        <ThemedText center type='defaultSemiBold'>
          {currentToken?.token?.name}
        </ThemedText>
      </View>

      <Card>
        <View>
          <ThemedText type='defaultLight'>Recipient address</ThemedText>

          <Spacer height={8} />

          <ThemedText type='defaultSemiBold'>{recipientAddress}</ThemedText>
        </View>

        <Spacer height={16} />

        <View>
          <ThemedText type='defaultLight'>Amount</ThemedText>

          <Spacer height={8} />

          <ThemedText type='defaultSemiBold'>
            {prettyNumber(amount)} {currentToken?.token?.symbol}
          </ThemedText>
        </View>
      </Card>
    </ScrollView>
  );
};

const useStyles = () => {
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
      gap: 16,
    },
    divider: {
      borderBottomWidth: 1,
      borderBottomColor: white65,
    },
    center: {
      alignItems: 'center',
    },
    tokenLogo: {
      width: 50,
      height: 50,
      borderRadius: 999,
      overflow: 'hidden',
    },
  });

  return { styles };
};
