import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { FormProvider, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { formatUnits } from 'viem';
import { CustomButton } from '@/components/Button';
import { Show } from '@/components/Show';
import { ViewSafeArea } from '@/components/ui/ViewSafeArea';
import { useCreateTxTransferToken } from '@/hooks/useCreateTxTransferToken';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';
import { toastError, toastSuccess } from '@/utils/toast';
import { ZOD_ERRORS } from '@/utils/zodError';
import { FormSendToken } from './component/FormSendToken';
import { SendTokenConfirm } from './component/SendTokenConfirm';
import { TransferFormData, transferTokenSchema } from './transferTokenSchema';

type Props = {};

export const SendToken = (props: Props) => {
  const { styles } = useStyles();

  const { data: safeTokens } = useGetSafeBalances({ limit: 10 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results) || [];
  const { mutateAsync: createTxTransferToken, isPending: isCreatingTx } = useCreateTxTransferToken();

  const methods = useForm<TransferFormData>({
    resolver: zodResolver(transferTokenSchema),
    defaultValues: {
      isConfirm: false,
    },
  });

  const { setValue, handleSubmit, setError, watch } = methods;
  const [isConfirm, token] = watch(['isConfirm', 'token']);
  const currentToken = tokens.find((t) => t.tokenAddress === token);

  async function handleConfirmSend(data: TransferFormData) {
    try {
      if (!currentToken || !currentToken.token) {
        return setError('token', { message: ZOD_ERRORS.required });
      }

      const currentTokenBalance = formatUnits(BigInt(currentToken?.balance), currentToken.token.decimals);
      if (Number(data.amount) > Number(currentTokenBalance)) {
        return setError('amount', { message: 'Insufficient balance' }, { shouldFocus: true });
      }

      if (!isConfirm) {
        setValue('isConfirm', true, { shouldValidate: true });
      } else {
        await createTxTransferToken({
          recipientAddress: data.recipientAddress,
          tokenAddress: token,
          amount: data.amount,
          decimal: currentToken.token.decimals,
        });

        toastSuccess('Transaction created successfully');

        router.back();
      }
    } catch (error) {
      toastError(error);
    }
  }

  async function handleBack() {
    setValue('isConfirm', false, { shouldValidate: true });
  }

  return (
    <FormProvider {...methods}>
      <GestureHandlerRootView>
        <BottomSheetModalProvider>
          <ViewSafeArea edges={['bottom']}>
            <Show when={!isConfirm}>
              <FormSendToken />
            </Show>

            <Show when={isConfirm}>
              <SendTokenConfirm currentToken={currentToken} />
            </Show>

            <View style={styles.actions}>
              <Show when={isConfirm}>
                <CustomButton type='secondary' onPress={handleBack} disabled={isCreatingTx}>
                  Back
                </CustomButton>
              </Show>

              <CustomButton onPress={handleSubmit(handleConfirmSend)} isLoading={isCreatingTx}>
                {isConfirm ? 'Confirm' : 'Continue'}
              </CustomButton>
            </View>
          </ViewSafeArea>
        </BottomSheetModalProvider>
      </GestureHandlerRootView>
    </FormProvider>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    actions: {
      padding: 10,
      gap: 8,
    },
  });

  return { styles };
};
