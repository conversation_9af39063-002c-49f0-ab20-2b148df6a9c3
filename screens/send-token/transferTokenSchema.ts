import { isAddress } from 'viem';
import { z } from 'zod';
import { ZOD_ERRORS } from '@/utils/zodError';

export const transferTokenSchema = z.object({
  isConfirm: z.boolean().optional(),
  recipientAddress: z
    .string(ZOD_ERRORS.required)
    .trim()
    .refine((val) => isAddress(val), ZOD_ERRORS.invalidAddress),
  token: z.string(ZOD_ERRORS.required).trim(),
  amount: z
    .string(ZOD_ERRORS.required)
    .trim()
    .regex(/^\d*\.?\d*$/, 'Please enter a valid number')
    .refine((val) => {
      const num = Number.parseFloat(val);
      return !isNaN(num) && num > 0;
    }, 'Amount must be greater than 0'),
});

export type TransferFormData = z.infer<typeof transferTokenSchema>;
