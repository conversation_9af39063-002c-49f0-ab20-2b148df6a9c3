import QRCode from 'react-fancy-qrcode';
import { StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';

type Props = {};

export const Receive = (props: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.container}>
      <Card>
        <ThemedText type='defaultSemiBold'>Receive Assets</ThemedText>

        <ThemedText type='smallLight'>
          This is the GeoSafe address. Please deposit funds by scanning the QR code or copying the address below.
        </ThemedText>

        <View>
          <QRCode
            value={'https://github.com/jgillick/react-fancy-qrcode'}
            size={200}
            dotScale={0.8}
            dotRadius='50%'
            positionRadius={['3%', '1%']}
            errorCorrection='H'
            // logo={require('images/fire.png')}
          />
        </View>
      </Card>
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
    },
  });

  return { styles };
};
