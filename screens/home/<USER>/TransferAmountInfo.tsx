import { TokenInfoResponse } from '@safe-global/api-kit';
import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { StyleSheet, View } from 'react-native';
import { formatUnits } from 'viem';
import { ThemedText } from '@/components/ThemedText';
import { prettyNumber } from '@/utils/common';
import { getTokenInfoFromTransaction } from '@/utils/safeTransaction';

type Props = {
  txData: SafeMultisigTransactionResponse;
  tokenInfo?: TokenInfoResponse;
};

export const TransferAmountInfo = ({ txData, tokenInfo }: Props) => {
  const tokenInfoFromTx = getTokenInfoFromTransaction(txData);

  if (!tokenInfoFromTx) return null;

  if (Array.isArray(tokenInfoFromTx))
    return (
      <View>
        <ThemedText type='smallMedium' center>
          {tokenInfoFromTx.length} actions
        </ThemedText>
      </View>
    );

  const formatAmount =
    tokenInfo && tokenInfoFromTx?.amount ? formatUnits(BigInt(tokenInfoFromTx?.amount), tokenInfo?.decimals || 0) : 0;

  return (
    <View>
      <ThemedText type='smallMedium' center>
        {prettyNumber(formatAmount)} {tokenInfo?.symbol}
      </ThemedText>
    </View>
  );
};

const _useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
      flexDirection: 'row',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
  });

  return { styles };
};
