import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, { FadeIn, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { Icons } from '@/assets/icons';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { useGetTokenInfo } from '@/hooks/useGetTokenInfo';
import { formatDateToNow } from '@/utils/date';
import {
  classifyByMethod,
  classifyByOperation,
  classifyByTypeAndValue,
  determineCategory,
} from '@/utils/safeTransaction';
import { TransactionDetail } from './TransactionDetail';
import { TransferAmountInfo } from './TransferAmountInfo';

type Props = {
  txData: SafeMultisigTransactionResponse;
};

export const Transaction = ({ txData }: Props) => {
  const { styles } = useStyles();
  const [isOpen, setIsOpen] = useState(false);

  const { data: tokenInfo } = useGetTokenInfo({ tokenAddress: txData.to });

  const operationType = classifyByOperation(txData);
  const valueType = classifyByTypeAndValue(txData);
  const methodType = classifyByMethod(txData);

  const category = determineCategory(valueType, methodType);

  const rotateAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotate: withSpring(isOpen ? '180deg' : '0deg', {
            stiffness: 200,
            damping: 100,
          }),
        },
      ],
    };
  });

  return (
    <View>
      <View style={styles.container}>
        <View>
          <ThemedText type='tinyLight'>{txData.nonce}</ThemedText>
        </View>

        <View style={styles.fullFlex}>
          <TransactionType type={operationType} category={category} />
        </View>

        <View style={styles.fullFlex}>
          <Show when={category === 'TOKEN_TRANSFER'}>
            <TransferAmountInfo txData={txData} tokenInfo={tokenInfo} />

            <ThemedText type='tinyLight' center>
              {txData.confirmations?.length}/{txData.confirmationsRequired} confirmation
            </ThemedText>
          </Show>
        </View>

        <View style={styles.fullFlex}>
          <ThemedText type='tinyLight' center>
            {formatDateToNow(txData.submissionDate)}
          </ThemedText>
        </View>

        <View style={[{ alignItems: 'flex-end' }]}>
          <TouchableOpacity onPress={() => setIsOpen((value) => !value)}>
            <Animated.View style={rotateAnimatedStyle}>
              <Icons.ChevronDown size={24} color='#fff' />
            </Animated.View>
          </TouchableOpacity>
        </View>
      </View>

      <Show when={isOpen}>
        <Animated.View entering={FadeIn}>
          <TransactionDetail txData={txData} tokenInfo={tokenInfo} category={category} />
        </Animated.View>
      </Show>
    </View>
  );
};

const TransactionType = ({
  type,
  category,
}: {
  type: ReturnType<typeof classifyByOperation>;
  category: ReturnType<typeof determineCategory>;
}) => {
  const { styles } = useStyles();

  const isSend = type === 'CALL' && (category === 'ETH_TRANSFER' || category === 'TOKEN_TRANSFER');

  return (
    <View style={styles.row}>
      <Show when={isSend}>
        <Icons.ArrowTopRight size={16} color='red' />
      </Show>

      <ThemedText type='smallMedium'>{isSend ? 'Send' : `${type} ${category}`}</ThemedText>
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    fullFlex: {
      flex: 1,
    },
    haftFlex: {
      flex: 0.5,
    },
  });

  return { styles };
};
