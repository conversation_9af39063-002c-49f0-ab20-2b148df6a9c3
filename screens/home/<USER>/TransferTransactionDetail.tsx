import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { format } from 'date-fns';
import * as Clipboard from 'expo-clipboard';
import { PropsWithChildren } from 'react';
import { Linking, StyleSheet, TouchableOpacity, View } from 'react-native';
import { formatUnits } from 'viem';
import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { CustomImage } from '@/components/ui/Image';
import { useGetTokenInfo } from '@/hooks/useGetTokenInfo';
import { prettyNumber, shortenString } from '@/utils/common';
import { getTokenInfoFromTransaction } from '@/utils/safeTransaction';
import { toastSuccess } from '@/utils/toast';
import { CURRENT_CHAIN } from '@/utils/web3';

type TransferInfoProps = {
  tokenAddress: string;
  amount: string;
  recipient: string;
};

export const TransferInfo = ({ tokenAddress, amount, recipient }: TransferInfoProps) => {
  const { data: tokenInfo } = useGetTokenInfo({ tokenAddress });

  const formatAmount = tokenInfo && amount ? formatUnits(BigInt(amount), tokenInfo?.decimals || 0).toString() : '-';

  return (
    <View>
      <Send logoUri={tokenInfo?.logoUri ?? ''} amount={formatAmount} symbol={tokenInfo?.symbol ?? ''} />

      <Spacer height={8} />

      <To recipient={recipient ?? ''} />
    </View>
  );
};

type Props = {
  txData: SafeMultisigTransactionResponse;
  tokenInfoFromTx: ReturnType<typeof getTokenInfoFromTransaction>;
};

export const TransferTransactionDetail = ({ txData, tokenInfoFromTx }: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.containerGap}>
      <TransactionHash txHash={txData.transactionHash ?? ''} />

      <Row title='Created:'>
        <ThemedText type='smallMedium'>{format(new Date(txData.submissionDate), 'PP')}</ThemedText>
      </Row>

      <Row title='Executed:'>
        <ThemedText type='smallMedium'>
          {txData.executionDate ? shortenString(txData.executionDate, 6, 4) : '-'}
        </ThemedText>
      </Row>

      <View style={styles.containerGap}>
        {Array.isArray(tokenInfoFromTx) ? (
          tokenInfoFromTx.map((tx, index) => (
            <View key={index}>
              <TransferInfo tokenAddress={tx.tokenAddress} amount={tx.amount ?? '0'} recipient={tx.recipient ?? ''} />
            </View>
          ))
        ) : (
          <TransferInfo
            tokenAddress={tokenInfoFromTx?.tokenAddress ?? ''}
            amount={tokenInfoFromTx?.amount ?? '0'}
            recipient={txData.to ?? ''}
          />
        )}
      </View>
    </View>
  );
};

const Row = ({ title, children }: PropsWithChildren<{ title: string }>) => {
  const { styles } = useStyles();
  return (
    <View style={styles.row}>
      <ThemedText type='smallLight'>{title}</ThemedText>

      {children}
    </View>
  );
};

const Send = ({ logoUri, amount, symbol }: { logoUri: string; amount: string; symbol: string }) => {
  const { styles } = useStyles();

  return (
    <Row title='Send:'>
      <CustomImage source={{ uri: logoUri }} style={styles.tokenLogo} />

      <ThemedText type='smallMedium'>
        {prettyNumber(amount)} {symbol}
      </ThemedText>
    </Row>
  );
};

const To = ({ recipient }: { recipient: string }) => {
  const { styles } = useStyles();

  const handleCopy = async () => {
    if (!recipient) return;

    await Clipboard.setStringAsync(recipient);
    toastSuccess('Copied to clipboard');
  };

  const handleDirect = () => {
    Linking.openURL(CURRENT_CHAIN.blockExplorers?.default?.url + `/address/${recipient}`);
  };

  console.log({ recipient });

  if (!recipient) return <Row title='Receive:'>-</Row>;

  return (
    <Row title='Receive:'>
      <ThemedText type='smallMedium' style={styles.address} onPress={handleDirect}>
        {shortenString(recipient, 6, 4)}
      </ThemedText>

      <TouchableOpacity activeOpacity={0.7} onPress={handleCopy}>
        <Icons.Copy size={16} color='#fff' />
      </TouchableOpacity>
    </Row>
  );
};

const TransactionHash = ({ txHash }: { txHash: string }) => {
  return (
    <Row title='Transaction hash:'>
      <ThemedText type='smallMedium'>{txHash ? shortenString(txHash, 6, 4) : '-'}</ThemedText>
    </Row>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    tokenLogo: {
      width: 24,
      height: 24,
      borderRadius: 999,
      overflow: 'hidden',
    },
    containerGap: {
      gap: 8,
    },
    address: {
      textDecorationStyle: 'solid',
      textDecorationLine: 'underline',
    },
  });

  return { styles };
};
