import * as Clipboard from 'expo-clipboard';
import { router } from 'expo-router';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import AvatarAddress from '@/components/ui/AvatarAddress';
import { Card } from '@/components/ui/Card';
import { useSafeWalletInfo } from '@/hooks/useSafeWalletInfo';
import { useTheme } from '@/hooks/useThemeColor';
import { useWalletStore } from '@/store/wallet';
import { shortenString } from '@/utils/common';
import { toastSuccess } from '@/utils/toast';

type Props = {};

export const AccountInfo = (props: Props) => {
  const { styles, colors } = useStyles();
  const { safeWallet } = useWalletStore();
  const { data: safeWalletInfo } = useSafeWalletInfo();
  const { threshold = 0, owners = [] } = safeWalletInfo || {};

  async function handleCopy(text: string) {
    if (!text) return;

    await Clipboard.setStringAsync(text);
    toastSuccess('Copied to clipboard');
  }

  function navigateReceiveScreen() {
    router.navigate('/(app)/receive');
  }

  return (
    <Card>
      <View style={styles.walletContainer}>
        <View style={styles.avatarContainer}>
          <AvatarAddress address={safeWallet!} />

          <Show when={!!safeWalletInfo}>
            <View style={styles.thresholdContainer}>
              <ThemedText type='tinyLight' style={styles.thresholdText}>
                {`${threshold}/${owners?.length}`}
              </ThemedText>
            </View>
          </Show>
        </View>

        <ThemedText type='defaultSemiBold'>{shortenString(safeWallet!, 6, 4)}</ThemedText>

        <TouchableOpacity activeOpacity={0.7} onPress={() => handleCopy(safeWallet!)}>
          <Icons.Copy size={20} color='#fff' />
        </TouchableOpacity>
      </View>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton type='primary' style={styles.buttonContainer}>
          <View style={styles.buttonContainer}>
            <Icons.ArrowTopRight size={20} color={colors.textButtonPrimary} />
            <ThemedText style={styles.buttonText}>Send</ThemedText>
          </View>
        </CustomButton>

        <CustomButton type='outlined' style={styles.buttonContainer} onPress={navigateReceiveScreen}>
          <View style={styles.buttonContainer}>
            <Icons.ArrowBottomLeft size={20} color={colors.primary} />
            <ThemedText style={styles.buttonTextOutlined}>Receive</ThemedText>
          </View>
        </CustomButton>
      </View>
    </Card>
  );
};

const useStyles = () => {
  const textButtonPrimary = useTheme('textButtonPrimary');
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    walletContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
    },
    avatarContainer: {},
    thresholdContainer: {
      position: 'absolute',
      top: -10,
      right: -10,
      padding: 2,
      backgroundColor: primary,
      borderRadius: 16,
      width: 24,
      height: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    thresholdText: {
      color: textButtonPrimary,
      fontSize: 10,
    },
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      minHeight: 38,
      justifyContent: 'center',
      flex: 1,
    },
    buttonText: {
      color: textButtonPrimary,
    },
    buttonTextOutlined: {
      color: primary,
    },
    actions: {
      flexDirection: 'row',
      gap: 8,
    },
  });

  return { styles, colors: { textButtonPrimary, primary } };
};
