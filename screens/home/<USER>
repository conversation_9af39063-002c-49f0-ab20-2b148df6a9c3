import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { Spacer } from '@/components/Spacer';
import { useBottomTabOverflow } from '@/components/ui/TabBarBackground';
import { Title } from '@/components/ui/Title';
import { ViewSafeArea } from '@/components/ui/ViewSafeArea';
import { queryKeys } from '@/utils/queryKeys';
import { toastError } from '@/utils/toast';
import { AccountInfo } from './components/AccountInfo';
import { PendingTransactions } from './components/PendingTransactions';
import { TopAssets } from './components/TopAssets';

type Props = {};

export const Home = (props: Props) => {
  const { styles } = useStyles();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const queryClient = useQueryClient();

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      // TODO: Refresh data
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queryKeys.safeTxsPending }),
        queryClient.invalidateQueries({ queryKey: queryKeys.safeWalletInfo }),
      ]);
    } catch (error) {
      toastError(error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <ViewSafeArea edges={['top']}>
      <View style={styles.container}>
        <Title fontSize={20} />

        <Spacer height={20} />

        <AccountInfo />

        <Spacer height={20} />

        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.scrollContainer}
          refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} tintColor={'#fff'} />}
        >
          <TopAssets />

          <Spacer height={20} />

          <PendingTransactions />
        </ScrollView>
      </View>
    </ViewSafeArea>
  );
};

const useStyles = () => {
  const bottom = useBottomTabOverflow();

  const styles = StyleSheet.create({
    container: {
      padding: 10,
      flex: 1,
      paddingBottom: bottom + 10,
    },
    scrollContainer: {
      flex: 1,
    },
  });

  return { styles };
};
