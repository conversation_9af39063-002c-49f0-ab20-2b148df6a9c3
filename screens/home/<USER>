import { StyleSheet, View } from 'react-native';
import { Spacer } from '@/components/Spacer';
import { Title } from '@/components/ui/Title';
import { ViewSafeArea } from '@/components/ui/ViewSafeArea';
import { AccountInfo } from './components/AccountInfo';
import { TopAssets } from './components/TopAssets';

type Props = {};

export const Home = (props: Props) => {
  const { styles } = useStyles();

  return (
    <ViewSafeArea edges={['top']}>
      <View style={styles.container}>
        <Title fontSize={20} />

        <Spacer height={20} />

        <AccountInfo />

        <Spacer height={20} />

        <TopAssets />
      </View>
    </ViewSafeArea>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
    },
  });

  return { styles };
};
