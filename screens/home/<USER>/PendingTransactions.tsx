import { FlatList, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { useGetSafeTxsPending } from '@/hooks/useGetSafeTxsPending';
import { Transaction } from './Transaction';

type Props = {};

export const PendingTransactions = (props: Props) => {
  const { styles } = useStyles();

  const { data: safeTxs } = useGetSafeTxsPending({ limit: 5 });
  const txs = safeTxs?.pages.flatMap((page) => page.results);

  console.log(txs);

  return (
    <Card style={styles.container}>
      <ThemedText type='defaultSemiBold'>Transaction Pending </ThemedText>

      <FlatList
        bounces={true}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ gap: 16 }}
        keyboardShouldPersistTaps='handled'
        data={txs}
        renderItem={({ item }) => <Transaction txData={item} />}
      />
    </Card>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
    },
  });

  return { styles };
};
