import { View } from 'react-native';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Asset } from '@/components/ui/Asset';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';

type Props = {};

export const TopAssets = (props: Props) => {
  const { data: safeTokens } = useGetSafeBalances({ limit: 5 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results);

  return (
    <View>
      <ThemedText type='defaultSemiBold'>Top Assets</ThemedText>

      <Spacer height={16} />

      {tokens
        ?.filter((asset) => asset.token)
        ?.map((asset) => (
          <View key={asset.tokenAddress}>
            <Asset
              logoUri={asset?.token?.logoUri!}
              name={asset?.token?.name!}
              symbol={asset?.token?.symbol!}
              balance={asset?.balance!}
              decimal={asset?.token?.decimals!}
            />
          </View>
        ))}
    </View>
  );
};
