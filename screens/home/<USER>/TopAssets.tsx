import { StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Asset } from '@/components/ui/Asset';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';

type Props = {};

export const TopAssets = (props: Props) => {
  const { styles } = useStyles();

  const { data: safeTokens } = useGetSafeBalances({ limit: 3 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results);

  return (
    <View style={styles.container}>
      <ThemedText type='defaultSemiBold'>Top Assets</ThemedText>

      {tokens?.map((asset) => (
        <View key={asset.tokenAddress}>
          <Asset
            logoUri={asset?.token?.logoUri!}
            name={asset?.token?.name!}
            symbol={asset?.token?.symbol!}
            balance={asset?.balance!}
            decimal={asset?.token?.decimals!}
          />
        </View>
      ))}
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
    },
  });

  return { styles };
};
