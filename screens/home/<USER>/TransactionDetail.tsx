import { TokenInfoResponse } from '@safe-global/api-kit';
import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { format } from 'date-fns';
import { PropsWithChildren, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { formatUnits } from 'viem';
import { privateKeyToAddress } from 'viem/accounts';
import { CustomButton } from '@/components/Button';
import { ModalConfirm } from '@/components/ModalConfirm';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { CustomImage } from '@/components/ui/Image';
import { useSafeContext } from '@/context/safeContext';
import { useApproveSafeTx } from '@/hooks/useApproveSafeTx';
import { useDenySafeTx } from '@/hooks/useDenySafeTx';
import { useExecuteSafeTx } from '@/hooks/useExecuteSafeTx';
import { useTheme } from '@/hooks/useThemeColor';
import { prettyNumber, shortenString } from '@/utils/common';
import { determineCategory, getTokenInfoFromTransaction } from '@/utils/safeTransaction';
import { toastError, toastSuccess } from '@/utils/toast';

type Props = {
  txData: SafeMultisigTransactionResponse;
  tokenInfo?: TokenInfoResponse;
  category: ReturnType<typeof determineCategory>;
};

export const TransactionDetail = ({ tokenInfo, txData, category }: Props) => {
  const { styles } = useStyles();
  const { signer } = useSafeContext();
  const signerAddress = privateKeyToAddress(`0x${signer?.privateKey}`);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const isConfirmed =
    (txData.confirmations || [])?.findIndex((sig) => sig.owner.toLowerCase() === signerAddress.toLowerCase()) > -1;
  const isCanExecute = !txData.isExecuted && Number(txData.confirmations?.length || 0) >= txData.confirmationsRequired;

  const { isPending: isPendingApprove, mutateAsync: approveTx } = useApproveSafeTx();
  const { isPending: isPendingDeny, mutateAsync: denyTx } = useDenySafeTx();
  const { isPending: isExecuting, mutateAsync: executeTx } = useExecuteSafeTx();

  async function handleExecute() {
    try {
      await executeTx({ safeTxHash: txData.safeTxHash });
      toastSuccess('Transaction executed successfully');
    } catch (error) {
      toastError(error);
    }
  }

  async function handleApprove() {
    try {
      await approveTx({ safeTxHash: txData.safeTxHash });
      toastSuccess('Transaction approved successfully');
    } catch (error) {
      toastError(error);
    }
  }

  async function handleDeny() {
    try {
      setIsModalVisible(false);
      await denyTx({ safeTxHash: txData.safeTxHash });
      toastSuccess('Transaction denied successfully');
    } catch (error) {
      toastError(error);
    }
  }

  return (
    <View>
      <Show when={category === 'ETH_TRANSFER' || category === 'TOKEN_TRANSFER'}>
        <TransferTransaction txData={txData} tokenInfo={tokenInfo} />
      </Show>

      <Show when={category === 'REJECTION'}>
        <RejectionTransaction txData={txData} />
      </Show>

      <Show when={!isConfirmed}>
        <Spacer height={16} />

        <View style={styles.actionContainer}>
          <CustomButton
            size='sm'
            type='primary'
            style={[styles.acceptButton, styles.fullFlex]}
            textStyle={styles.textWhite}
            textType='tinyMedium'
            isLoading={isPendingApprove}
            onPress={handleApprove}
            disabled={isPendingDeny}
          >
            Approve
          </CustomButton>

          <Show when={category !== 'REJECTION'}>
            <CustomButton
              size='sm'
              type='primary'
              style={[styles.denyButton, styles.fullFlex]}
              textStyle={styles.textWhite}
              textType='tinyMedium'
              isLoading={isPendingDeny}
              disabled={isPendingApprove}
              onPress={() => setIsModalVisible(true)}
            >
              Deny
            </CustomButton>
          </Show>
        </View>
      </Show>

      <Show when={isCanExecute}>
        <Spacer height={16} />

        <CustomButton
          size='sm'
          type='primary'
          style={[styles.acceptButton, styles.fullFlex]}
          textStyle={styles.textWhite}
          textType='tinyMedium'
          isLoading={isExecuting}
          onPress={handleExecute}
        >
          Execute
        </CustomButton>
      </Show>

      <ModalConfirm
        title='Reject transaction'
        isVisible={isModalVisible}
        description='Propose an on-chain cancellation transaction'
        onCancel={() => setIsModalVisible(false)}
        onConfirm={handleDeny}
      />
    </View>
  );
};

const RejectionTransaction = ({ txData }: Pick<Props, 'txData'>) => {
  return (
    <View>
      <Spacer height={16} />

      <ThemedText type='tinyLight'>
        This is an on-chain rejection that won't send any funds. Executing this on-chain rejection will replace all
        currently awaiting transactions with nonce {txData.nonce}
      </ThemedText>
    </View>
  );
};

const TransferTransaction = ({ txData, tokenInfo }: Pick<Props, 'txData' | 'tokenInfo'>) => {
  const tokenInfoFromTx = getTokenInfoFromTransaction(txData);

  if (Array.isArray(tokenInfoFromTx))
    return (
      <View>
        <Spacer height={16} />

        <ThemedText>Call multiSend</ThemedText>
      </View>
    );

  const formatAmount =
    tokenInfo && tokenInfoFromTx?.amount ? formatUnits(BigInt(tokenInfoFromTx?.amount), tokenInfo?.decimals || 0) : 0;

  return (
    <View>
      <Spacer height={16} />

      <TransactionHash txHash={txData.transactionHash ?? ''} />

      <Spacer height={8} />

      <Row title='Created:'>
        <ThemedText type='smallMedium'>{format(new Date(txData.submissionDate), 'PP')}</ThemedText>
      </Row>

      <Spacer height={8} />

      <Row title='Executed:'>
        <ThemedText type='smallMedium'>
          {txData.executionDate ? shortenString(txData.executionDate, 6, 4) : '-'}
        </ThemedText>
      </Row>

      <Spacer height={8} />

      <Send logoUri={tokenInfo?.logoUri ?? ''} amount={formatAmount.toString()} symbol={tokenInfo?.symbol ?? ''} />

      <Spacer height={8} />

      <To recipient={tokenInfoFromTx?.recipient ?? ''} />
    </View>
  );
};

const Row = ({ title, children }: PropsWithChildren<{ title: string }>) => {
  const { styles } = useStyles();
  return (
    <View style={styles.row}>
      <ThemedText type='smallLight'>{title}</ThemedText>

      {children}
    </View>
  );
};

const Send = ({ logoUri, amount, symbol }: { logoUri: string; amount: string; symbol: string }) => {
  const { styles } = useStyles();

  return (
    <Row title='Send:'>
      <CustomImage source={{ uri: logoUri }} style={styles.tokenLogo} />

      <ThemedText type='smallMedium'>
        {prettyNumber(amount)} {symbol}
      </ThemedText>
    </Row>
  );
};

const To = ({ recipient }: { recipient: string }) => {
  return (
    <Row title='Receive:'>
      <ThemedText type='smallMedium'>{shortenString(recipient, 6, 4)}</ThemedText>
    </Row>
  );
};

const TransactionHash = ({ txHash }: { txHash: string }) => {
  return (
    <Row title='Transaction hash:'>
      <ThemedText type='smallMedium'>{txHash ? shortenString(txHash, 6, 4) : '-'}</ThemedText>
    </Row>
  );
};

const useStyles = () => {
  const red = useTheme('red');
  const red30 = useTheme('red30');
  const primary = useTheme('primary');
  const primary30 = useTheme('primary30');

  const styles = StyleSheet.create({
    container: {
      gap: 16,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    tokenLogo: {
      width: 24,
      height: 24,
      borderRadius: 999,
      overflow: 'hidden',
    },
    denyButton: {
      backgroundColor: red30,
      borderWidth: 1,
      borderColor: red,
    },
    textWhite: {
      color: '#fff',
    },
    acceptButton: {
      backgroundColor: primary30,
      borderWidth: 1,
      borderColor: primary,
    },
    actionContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    fullFlex: {
      flex: 1,
    },
  });

  return { styles };
};
