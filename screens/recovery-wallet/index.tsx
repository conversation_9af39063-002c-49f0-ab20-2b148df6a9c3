import { getApp } from '@react-native-firebase/app';
import { getToken } from '@react-native-firebase/messaging';
import { CommonActions } from '@react-navigation/native';
import Safe from '@safe-global/protocol-kit';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import { useMemo, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import QuickCrypto from 'react-native-quick-crypto';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Address, Hex, pad } from 'viem';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { DEFAULT_VALIDITY_BLOCKS, TRecoveryKey } from '@/hooks/useAddRecoveryKeys';
import { TWallet, useCreateWallet } from '@/hooks/useCreateWallet';
import { useExecuteRecoveryWallet } from '@/hooks/useExecuteRecoveryWallet';
import { useGetActiveKeyCount } from '@/hooks/useGetActiveKeyCount';
import { useGetCodeBook } from '@/hooks/useGetCodeBook';
import { useGetLocation } from '@/hooks/useGetLocation';
import { useGetRecoveriesPending } from '@/hooks/useGetRecoveriesPending';
import { useGetSafeConfig } from '@/hooks/useGetSafeConfig';
import { useInitRecoveryWallet } from '@/hooks/useInitRecoveryWallet';
import { useSetupRecoveryKey } from '@/hooks/useSetupRecoveryKey';
import { useSetupWallet } from '@/hooks/useSetupWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { useWalletStore } from '@/store/wallet';
import { env } from '@/utils/env';
import { toastError, toastSuccess } from '@/utils/toast';
import { pkToAccount } from '@/utils/web3';
import { RecoverySteps } from './components/RecoverySteps';

type Props = {};

export const RecoveryWallet = (props: Props) => {
  const { styles } = useStyles();
  const { safeAddress, recoveryKey } = useLocalSearchParams<{
    safeAddress: Address;
    recoveryKey: Hex;
  }>();

  const [isPending, setIsPending] = useState(false);

  const { setSafeWallet } = useWalletStore();
  const navigation = useNavigation();

  const { mutateAsync: setupRecoveryKey } = useSetupRecoveryKey();
  const { mutateAsync: setupWallet } = useSetupWallet();
  // const { mutateAsync: verifyRecoveryKey } = useVerifyRecoveryKey();
  const { mutateAsync: getCodeBook, data: codeBook } = useGetCodeBook();
  const { mutateAsync: getLocation, data: location } = useGetLocation();
  const { mutateAsync: createWallet } = useCreateWallet();
  const { mutateAsync: initRecoveryWallet } = useInitRecoveryWallet();
  const { mutateAsync: executeRecoveryWallet } = useExecuteRecoveryWallet();
  const { mutateAsync: getRecoveriesPending } = useGetRecoveriesPending();
  const { mutateAsync: getActiveKeyCount } = useGetActiveKeyCount();
  const { mutateAsync: getSafeConfig } = useGetSafeConfig();

  async function handleRecoveryWallet() {
    try {
      setIsPending(true);

      // const isCanUse = await verifyRecoveryKey({ recoveryKey: `0x${recoveryKey}`, safeAddress });
      // if (!isCanUse) {
      //   throw new Error('Cannot use recovery key');
      // }

      if (!codeBook) {
        return await getCodeBook({ recoveryKey: `0x${recoveryKey}` });
      }

      if (!location) {
        return await getLocation();
      }

      const [, , expectedAddress, , , , stretchedCount] = codeBook;

      // Recovery wallet
      const recoveryKeyHex = `0x${recoveryKey}` as const;
      const recoveryWallet = await createWallet({ recoveryKey, location, stretchingCount: Number(stretchedCount) });
      const signerPk = `0x${recoveryWallet?.privateKey}` as const;
      const signer = pkToAccount(signerPk);
      const recoveryWalletAddress = signer.address;

      if (recoveryWalletAddress !== expectedAddress) {
        throw new Error('Recovery wallet address mismatch');
      }

      // Gen new wallets
      let newWallet: TWallet | null = null;
      let newRecoveryKey: string | null = null;

      let [, recoveryKeyId, recoveryId, , , executed, cancelled] = await getRecoveriesPending({
        recoveryKey: recoveryKeyHex,
      });

      if (executed || cancelled) {
        throw new Error('Recovery key already executed or cancelled');
      }

      let protocolKit = await Safe.init({
        provider: env.RPC_URL,
        safeAddress,
        signer: signerPk,
      });

      // initial recovery wallet step
      if (recoveryId === pad('0x0')) {
        const totalKeysActive = await getActiveKeyCount({ safeAddress });
        const { minKeysRequired } = await getSafeConfig({ safeAddress });

        const countNewKeys = Math.max(Number(minKeysRequired) - Number(totalKeysActive) + 1, 1);

        const newKeys: TRecoveryKey[] = [];

        for (let index = 0; index < countNewKeys; index++) {
          newRecoveryKey = QuickCrypto.randomBytes(32).toString('hex');
          newWallet = await createWallet({ recoveryKey: newRecoveryKey, location });
          const newRecoveryKeyHex = `0x${newRecoveryKey}` as const;
          const newSignerPk = `0x${newWallet?.privateKey}` as const;
          const newSigner = pkToAccount(newSignerPk);

          newKeys.push({
            keyId: newRecoveryKeyHex,
            safe: safeAddress,
            expectedAddress: newSigner.address,
            validUntilBlock: BigInt(DEFAULT_VALIDITY_BLOCKS),
            isUsed: false,
            hint: '',
            stretchedCount: Number(newWallet?.stretchingCount!),
          });
        }

        const receipt = await initRecoveryWallet({
          safeAddress,
          recoveryKey: recoveryKeyHex,
          newKeys,
          protocolKit,
        });

        if (receipt.status !== 'success') {
          throw new Error('Init recovery wallet failed');
        }

        toastSuccess('Init recovery wallet success');
      } else {
        const recoveryKey = recoveryKeyId.substring(2);
        newRecoveryKey = recoveryKey;
        newWallet = await createWallet({ recoveryKey: recoveryKey, location });
      }

      if (!newWallet || !newRecoveryKey) {
        throw new Error('No new signer created');
      }

      // execute transaction recovery wallet
      const receipt = await executeRecoveryWallet({
        recoveryId: recoveryKeyHex,
        protocolKit,
      });

      const newSignerPk = `0x${newWallet?.privateKey}` as const;

      await protocolKit.connect({
        safeAddress,
        signer: newSignerPk,
      });

      if (receipt.status !== 'success') {
        throw new Error('Execute recovery wallet failed');
      }

      toastSuccess('Execute recovery wallet success');

      // Store safe address local
      setSafeWallet(safeAddress);

      // Store new signer local
      await setupRecoveryKey(newRecoveryKey);
      await setupWallet({
        privateKey: newWallet.privateKey,
        seedPhrase: newWallet.seedPhrase,
        recoveryKey: newRecoveryKey,
      });

      const firebaseApp = getApp();
      const _token = await getToken(firebaseApp.messaging());

      toastSuccess('Wallet recovered successfully');

      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: '(app)' }],
        })
      );
    } catch (error) {
      toastError(error);
    } finally {
      setIsPending(false);
    }
  }

  const actionTitle = useMemo(() => {
    if (!codeBook) return 'Prepare code-book';

    if (!location) return 'Get location';

    return 'Recover wallet';
  }, [codeBook, location]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView>
        <View style={styles.containerKey}>
          <View style={styles.boxKey}>
            <ThemedText type='tinyLight' style={styles.keyTitle}>
              GeoSafe wallet address
            </ThemedText>

            <ThemedText>{safeAddress?.toString()}</ThemedText>
          </View>
        </View>

        <Spacer height={16} />

        <View style={styles.containerKey}>
          <View style={styles.boxKey}>
            <ThemedText type='tinyLight' style={styles.keyTitle}>
              Recovery key
            </ThemedText>

            <ThemedText>{recoveryKey?.toString()}</ThemedText>
          </View>
        </View>

        <Spacer height={16} />

        <ThemedText type='smallLight'>Recovery wallet steps</ThemedText>

        <Spacer height={16} />

        <RecoverySteps hasCodeBook={!!codeBook} hasLocation={!!location} />
      </ScrollView>

      <CustomButton type='primary' onPress={handleRecoveryWallet} isLoading={isPending}>
        {actionTitle}
      </CustomButton>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
    },
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
  });

  return { styles };
};
