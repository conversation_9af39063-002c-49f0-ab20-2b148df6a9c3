import * as Notifications from 'expo-notifications';
import { Href, router } from 'expo-router';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useTheme } from '@/hooks/useThemeColor';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowBanner: true,
    shouldShowList: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

type Props = {
  nextScreen: Href;
};

export const SetUpNotification = ({ nextScreen }: Props) => {
  const { styles } = useStyles();

  const handleNextScreen = () => {
    router.replace(nextScreen);
  };

  const requestNotification = async () => {
    let settings = await Notifications.getPermissionsAsync();

    if (!settings.granted) {
      settings = await Notifications.requestPermissionsAsync();
    }

    if (settings.granted) {
      handleNextScreen();
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
        <Spacer height={81} />

        <Icons.Notification size={56} />

        <Spacer height={24} />

        <ThemedText type='title'>Enable notifications</ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight'>Get notified for everything that matters with your account.</ThemedText>
      </ScrollView>

      <Spacer height={16} />

      <View style={styles.disclaimerContainer}>
        <ThemedText type='smallMedium'>Disclaimer</ThemedText>

        <ThemedText type='tinyLight'>
          We will not share your data or biometric information with any 3rd party. Your information is used locally to
          access your account.
        </ThemedText>
      </View>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton type='primary' onPress={requestNotification}>
          Enable Notification
        </CustomButton>

        <CustomButton type='secondary' onPress={handleNextScreen}>
          Maybe later
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white04 = useTheme('white04');

  const styles = StyleSheet.create({
    disclaimerContainer: {
      backgroundColor: white04,
      borderRadius: 16,
      padding: 16,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
  });

  return { styles };
};
