{"expo": {"name": "GeoSafe", "slug": "geo-safe", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "geosafe", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.hoailevar.geosafe", "buildNumber": "3"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.hoailevar.geosafe", "versionCode": 8}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#15151F", "imageWidth": 200}], "expo-secure-store", "expo-font", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow GeoSafe to use your location."}]], "experiments": {"typedRoutes": true}}}