import bigDecimal from 'js-big-decimal';
import { Image, StyleSheet, View } from 'react-native';
import { formatUnits } from 'viem';
import { useTheme } from '@/hooks/useThemeColor';
import { ThemedText } from '../ThemedText';

type Props = {
  logoUri: string;
  name: string;
  symbol: string;
  balance: string;
  decimal: number;
};

export const Asset = ({ logoUri, name, symbol, balance, decimal }: Props) => {
  const { styles } = useStyles();

  const formatBalance = formatUnits(BigInt(balance), decimal);

  return (
    <View style={styles.container}>
      <View style={styles.tokenContainer}>
        <Image source={{ uri: logoUri }} style={styles.logo} />

        <View>
          <ThemedText type='defaultSemiBold'>{name}</ThemedText>
          <ThemedText type='smallLight'>{symbol}</ThemedText>
        </View>
      </View>

      <ThemedText type='defaultSemiBold'>{bigDecimal.getPrettyValue(formatBalance)}</ThemedText>
    </View>
  );
};

const useStyles = () => {
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 8,
    },
    tokenContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
    },
    logo: {
      width: 36,
      height: 36,
      backgroundColor: white35,
      borderRadius: 999,
    },
  });

  return { styles };
};
