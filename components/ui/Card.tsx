import { PropsWithChildren } from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const Card = ({ children }: PropsWithChildren<Props>) => {
  const { styles } = useStyles();

  return <View style={styles.container}>{children} </View>;
};

const useStyles = () => {
  const cardBg = useTheme('card');

  const styles = StyleSheet.create({
    container: {
      backgroundColor: cardBg,
      padding: 16,
      borderRadius: 16,
    },
  });

  return {
    styles,
  };
};
